# Environment Configuration Example
# Copy this file to .env.local and update with your actual values

# App Configuration
EXPO_PUBLIC_APP_NAME=BlindTreasure
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_ENVIRONMENT=development

# API Configuration
EXPO_PUBLIC_API_BASE_URL=https://api.example.com
EXPO_PUBLIC_API_TIMEOUT=10000

# Database Configuration (if using)
# DATABASE_URL=your_database_url_here

# Authentication (if using services like Firebase, Auth0, etc.)
# EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
# EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
# EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_project_id

# Third-party Services
# EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
# EXPO_PUBLIC_ANALYTICS_ID=your_analytics_id

# Development Settings
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_LEVEL=debug

# Build Configuration
# EAS_PROJECT_ID=your_eas_project_id
