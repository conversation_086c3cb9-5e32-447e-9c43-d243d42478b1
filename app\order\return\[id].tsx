import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  Image,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Mock data cho đơn hàng trả hàng
const mockReturnOrder = {
  id: '2506160MGMW3A6Q',
  status: 'Hoàn tiền thành công',
  refundAmount: 143000,
  refundMethod: 'Đến Ví ShopeePay',
  timeline: {
    accepted: '4 Th02 2025',
    processing: '4 Th02 2025',
    completed: 'trước 5 Th02'
  },
  shop: {
    name: 'Thế Giới Skincare Chính Hãng',
    isOfficial: true
  },
  product: {
    name: 'Kem chống nắng Skin1004 chiết xuất rau má cho da dầu, da kh<PERSON> nhạy cảm...',
    quantity: 1,
    image: 'https://via.placeholder.com/80x80/FFE4E1/FF6B35?text=Skincare'
  },
  refundNote: 'Yêu cầu hoàn tiền của bạn đã được Shopee xử lý. Với đơn hoàn tiền về Thẻ tín dụng/ghi nợ, Apple Pay/Google Pay, sẽ cần thêm 7-14 ngày để ngân hàng cập nhật tiền hoàn. Bạn có thể liên hệ ngân hàng để kiểm tra ngày cập nhật cụ thể nhé.'
};

const formatCurrency = (amount: number) => {
  return `đ${amount.toLocaleString('vi-VN')}`;
};

export default function ReturnOrderScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const [showMore, setShowMore] = useState(false);

  const handleViewHistory = () => {
    Alert.alert('Lịch sử chat', 'Xem lịch sử chat với shop');
  };

  const handleShowMore = () => {
    setShowMore(!showMore);
  };

  return (
    <View className="flex-1 bg-gray-50" style={{ paddingTop: insets.top }}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <View className="bg-white px-4 py-3 border-b border-gray-100">
        <View className="flex-row items-center">
          <TouchableOpacity onPress={() => router.back()} className="mr-3">
            <Ionicons name="arrow-back" size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-lg font-medium text-gray-900">Chi tiết Trả hàng/Hoàn tiền</Text>
        </View>
      </View>

      <ScrollView className="flex-1">
        {/* Success Status */}
        <View className="bg-green-500 mx-4 mt-4 rounded-lg p-4">
          <Text className="text-white font-bold text-lg text-center">
            {mockReturnOrder.status}
          </Text>
        </View>

        {/* Refund Amount */}
        <View className="bg-white mx-4 mt-4 rounded-lg p-4">
          <View className="items-center">
            <TouchableOpacity className="flex-row items-center mb-2">
              <Text className="text-gray-500 text-sm">Chi tiết hoàn tiền</Text>
              <Ionicons name="chevron-forward" size={16} color="#8E8E93" className="ml-1" />
            </TouchableOpacity>
            <Text className="text-gray-900 font-bold text-2xl mb-1">
              {formatCurrency(mockReturnOrder.refundAmount)}
            </Text>
            <Text className="text-gray-500 text-sm">{mockReturnOrder.refundMethod}</Text>
          </View>
        </View>

        {/* Timeline */}
        <View className="bg-white mx-4 mt-4 rounded-lg p-4">
          <View className="flex-row items-center justify-between mb-6">
            {/* Step 1 */}
            <View className="items-center flex-1">
              <View className="w-12 h-12 bg-green-100 rounded-full items-center justify-center mb-2">
                <View className="w-6 h-6 bg-green-500 rounded-full items-center justify-center">
                  <Ionicons name="checkmark" size={16} color="white" />
                </View>
                <Ionicons name="storefront" size={20} color="#10B981" style={{ position: 'absolute' }} />
              </View>
              <Text className="text-xs text-gray-900 font-medium text-center">Chấp nhận hoàn tiền</Text>
              <Text className="text-xs text-gray-500 text-center">{mockReturnOrder.timeline.accepted}</Text>
            </View>

            {/* Connector 1 */}
            <View className="flex-1 h-0.5 bg-green-500 mx-2"></View>

            {/* Step 2 */}
            <View className="items-center flex-1">
              <View className="w-12 h-12 bg-green-100 rounded-full items-center justify-center mb-2">
                <View className="w-6 h-6 bg-green-500 rounded-full items-center justify-center">
                  <Ionicons name="checkmark" size={16} color="white" />
                </View>
                <Ionicons name="business" size={20} color="#10B981" style={{ position: 'absolute' }} />
              </View>
              <Text className="text-xs text-gray-900 font-medium text-center">Đang hoàn tiền</Text>
              <Text className="text-xs text-gray-500 text-center">{mockReturnOrder.timeline.processing}</Text>
            </View>

            {/* Connector 2 */}
            <View className="flex-1 h-0.5 bg-green-500 mx-2"></View>

            {/* Step 3 */}
            <View className="items-center flex-1">
              <View className="w-12 h-12 bg-green-100 rounded-full items-center justify-center mb-2">
                <View className="w-6 h-6 bg-green-500 rounded-full items-center justify-center">
                  <Ionicons name="checkmark" size={16} color="white" />
                </View>
                <Ionicons name="card" size={20} color="#10B981" style={{ position: 'absolute' }} />
              </View>
              <Text className="text-xs text-gray-900 font-medium text-center">Đã hoàn tiền</Text>
              <View className="bg-green-500 px-2 py-1 rounded mt-1">
                <Text className="text-white text-xs font-medium">{mockReturnOrder.timeline.completed}</Text>
              </View>
            </View>
          </View>

          {/* Refund Note */}
          <Text className="text-gray-600 text-sm leading-5">
            {mockReturnOrder.refundNote}
          </Text>
        </View>

        {/* Chat History */}
        <View className="bg-white mx-4 mt-4 rounded-lg">
          <TouchableOpacity 
            className="flex-row items-center justify-between p-4"
            onPress={handleViewHistory}
          >
            <Text className="text-gray-900 font-medium">Xem lịch sử chat</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>
        </View>

        {/* Shop Info */}
        <View className="bg-white mx-4 mt-4 rounded-lg p-4">
          <View className="flex-row items-center mb-3">
            <View className="w-6 h-6 bg-gray-800 rounded items-center justify-center mr-2">
              <Ionicons name="storefront" size={16} color="white" />
            </View>
            <Text className="text-gray-900 font-medium flex-1">{mockReturnOrder.shop.name}</Text>
            <Ionicons name="chevron-forward" size={16} color="#8E8E93" />
          </View>

          {/* Product */}
          <View className="flex-row">
            <Image 
              source={{ uri: mockReturnOrder.product.image }}
              className="w-16 h-16 rounded-lg mr-3"
            />
            <View className="flex-1">
              <Text className="text-gray-900 text-sm" numberOfLines={showMore ? undefined : 2}>
                {mockReturnOrder.product.name}
              </Text>
              <Text className="text-gray-500 text-right mt-2">x{mockReturnOrder.product.quantity}</Text>
            </View>
          </View>

          {/* Show More Button */}
          <TouchableOpacity 
            className="items-center mt-4 py-2"
            onPress={handleShowMore}
          >
            <View className="flex-row items-center">
              <Text className="text-gray-500 text-sm mr-1">
                {showMore ? 'Thu gọn' : 'Xem thêm'}
              </Text>
              <Ionicons 
                name={showMore ? "chevron-up" : "chevron-down"} 
                size={16} 
                color="#8E8E93" 
              />
            </View>
          </TouchableOpacity>
        </View>

        <View className="h-6"></View>
      </ScrollView>
    </View>
  );
}
