{"expo": {"name": "Blindtreasure", "slug": "Blindtreasure", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/favicon.png", "scheme": "Blindtreasure", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/favicon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "resizeMode": "cover", "backgroundColor": "#ffffff", "enableFullscreenImage_legacy": true}], ["expo-font", {"fonts": ["./assets/fonts/Rubik-Bold.ttf", "./assets/fonts/Rubik-ExtraBold.ttf", "./assets/fonts/Rubik-Medium.ttf", "./assets/fonts/Rubik-Regular.ttf", "./assets/fonts/Rubik-Light.ttf", "./assets/fonts/Rubik-SemiBold.ttf"]}]], "experiments": {"typedRoutes": true}}}